import React, { useEffect, useState } from "react";
import "../src/styles/App.scss";
import Container from "@mui/material/Container";
import Introduction from "./components/Introduction";
import Footer from "./components/Footer";
import AboutMe from "./components/AboutMe";
import ProjectsSection from "./components/ProjectsSection";
import ContactMe from "./components/ContactMe";
import Nav from "./components/Nav";
import Skills from "./components/Skills";

const App = () => {
    const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
    const [scrollY, setScrollY] = useState(0);

    // Start at the top of the page
    useEffect(() => {
        window.scrollTo(0, 0);
    }, []);

    // Track mouse position for lightsaber cursor effect
    useEffect(() => {
        const handleMouseMove = (e: MouseEvent) => {
            setMousePosition({ x: e.clientX, y: e.clientY });
        };

        window.addEventListener('mousemove', handleMouseMove);
        return () => window.removeEventListener('mousemove', handleMouseMove);
    }, []);

    // Track scroll position for parallax effect
    useEffect(() => {
        const handleScroll = () => {
            setScrollY(window.scrollY);
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    console.log("These aren't the droids you're looking for... but this portfolio might be what you seek!");

    return (
        <div
            className="App"
            style={{
                transform: `translateY(${scrollY * 0.1}px)`, // Subtle parallax effect
            }}
        >
            {/* Enhanced starfield with parallax */}
            <div
                className="enhanced-starfield"
                style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    transform: `translateY(${scrollY * 0.2}px)`,
                    zIndex: -2,
                    opacity: 0.6,
                }}
            />

            {/* Lightsaber cursor effect */}
            <div
                className="lightsaber-cursor-trail"
                style={{
                    left: mousePosition.x - 2,
                    top: mousePosition.y - 10,
                    position: 'fixed',
                    width: '4px',
                    height: '20px',
                    background: 'linear-gradient(to bottom, #00ff41, transparent)',
                    borderRadius: '2px',
                    pointerEvents: 'none',
                    zIndex: 9999,
                    boxShadow: '0 0 10px #00ff41',
                    transition: 'all 0.1s ease',
                }}
            />
            <Container component="main" className="contactContainer">
                <Nav />
                <Introduction />
                <AboutMe />
                <Skills />
                <ProjectsSection />
                <ContactMe />
            </Container>
            <Footer />
        </div>
    );
};

export default App;
