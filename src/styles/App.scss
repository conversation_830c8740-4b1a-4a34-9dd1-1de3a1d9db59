@import url("https://fonts.googleapis.com/css2?family=Fira+Code:wght@500&family=JetBrains+Mono:wght@600&display=swap");

.App {
    text-align: center;
    font-family: "JetBrains Mono", monospace;
    font-size: calc(1em + 1vw);
    margin: 0;
    margin-top: 0px;
    padding: 0;
    box-sizing: border-box;
    height: 100%;
    width: 100%;
    background-color: #363636;

    header {
        padding: 0 !important;
    }

    p {
        color: #aba7a7;
    }

    .nav {
        display: grid;
        margin-bottom: 1vh;
    }

    .bar {
        position: fixed;
        top: 50%;
        right: 0;
        -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }

    .bar a {
        display: block;
        text-align: center;
        padding: 16px;
        transition: all 0.3s ease;
        color: white;
        opacity: 25%;
    }

    .bar a:hover {
        opacity: 100%;
    }

    .nav-wrapper {
        position: fixed;
        top: 0;
        right: 0;
        text-align: right;
        font-size: 1rem;
        z-index: 1;
        margin-right: 5vw;
        margin-top: 5vh;

        a {
            margin: 4px;
        }
    }

    .nav-wrapper-mobile {
        padding-left: 3vw;
        text-align: right;
        top: 0;
        right: 0;
        margin-bottom: 2em;
        font-size: 1em;
        margin-top: 1em;

        a {
            margin: 4px;
        }

        hr {
            height: 0.05em;
            width: 60vw;
            background-color: rgb(27, 226, 27);
            margin-top: 10vw;
        }
    }

    .fade-in-section {
        opacity: 0;
        transform: translateY(10vh);
        visibility: hidden;
        transition: opacity 0.6s ease-out, transform 1s ease-out;
        will-change: opacity, visibility;
    }
    .fade-in-section.is-visible {
        opacity: 1;
        transform: none;
        visibility: visible;
    }

    a {
        text-decoration: none;
        color: lime;
    }

    a:hover {
        color: white;
    }

    .name {
        text-align: left;
        margin-top: 1rem;
        font-size: clamp(2rem, 3em, 6rem);
        margin-bottom: 0;

        b {
            color: #aba7a7;
        }
    }

    .special {
        color: lime;
    }

    .closing-symbol {
        margin: 0 auto;
        font-size: 1.2em;
    }

    .guideArrow {
        font-size: 3rem;
        display: block;
        border-radius: 1rem !important;
        color: rgb(27, 226, 27);
        top: 50%;
        left: 40%;
    }

    .display-pic {
        margin-top: 1.5rem;
        width: 20rem;
        height: 20rem;
        border-radius: 1rem;
    }

    .headline {
        margin-top: 0;
        text-align: left;
        font-size: clamp(0.5rem, 1.3em, 3.5rem);
        color: white;
    }

    .call-to-action-button {
        height: 3em;
        width: 10em;
        font-size: 1em;
        border-color: lime;
        font-family: "JetBrains Mono", monospace;
        color: lime;
        text-transform: none;
        position: relative;
    }

    .call-to-action-button:hover {
        color: lime;
        border-color: lime;
    }

    .sub-heading {
        font-family: "Fira Mono", monospace;
        margin-top: 1rem;
        margin-bottom: 1rem;
        text-align: left;
        font-size: clamp(0.7rem, 1.05em, 3rem);
        color: white;
    }

    .sub-heading::after {
        display: inline-block;
        height: 2px;
        width: 300px;
        margin-left: 1vw;
        // background-color: rgb(27, 226, 27);
        background-color: #aba7a7;
        content: "";
    }

    .list-item {
        font-size: clamp(0.5rem, 1.1rem, 1.5rem);
        color: #aba7a7;
    }

    .resume-button {
        color: rgb(27, 226, 27);
        border-color: rgb(27, 226, 27);
    }

    .resume-button:hover {
        color: black;
        border-color: black;
        background-color: whitesmoke;
    }

    .socials {
        margin-top: 1rem;
        position: relative;
        text-align: center;
        height: 1rem;

        .favicon {
            color: black;
            margin: 0.5rem;
            height: 2rem;
            width: 1.3em;
            transition: transform 0.5s ease;
        }

        .favicon:hover {
            text-decoration: none;
            transform: scale(1.3);
        }
    }

    .intro {
        margin-top: 20vh;
        margin-bottom: 50vh;
        width: 60vw;
        margin-left: 0;
    }

    .intro-mobile {
        margin-top: 5rem;
        margin-bottom: 5rem;
    }

    .section {
        margin-top: 5vh;
        margin-bottom: 5vh;
    }

    .section-mobile {
        margin-top: 5vh;
        margin-bottom: 5vh;
    }

    .skillsGrid {
        margin: 0 auto;
        width: 100%;

        .skill {
            height: 2.5rem;
            width: 2.5rem;
            fill: white;
            margin: 1em;
            transition: transform 0.5s ease;
        }

        .skill:hover {
            transform: scale(1.3);
        }
    }

    .projectGrid {
        margin: 0 auto;

        .project {
            margin-top: 0.5rem;

            .project-card {
                height: 17rem;
                border-radius: 1rem;
                width: 20rem;
                background-color: #474747;
                color: whitesmoke;

                button {
                    width: 100%;
                    height: 100%;

                    span {
                        height: 0px !important;
                    }
                }

                h1 {
                    font-family: "Fira Mono", monospace;
                }

                p {
                    font-size: 0.8rem;
                    font-family: "JetBrains Mono", monospace;
                    color: white;
                }
            }
        }
    }
}

.contactContainer {
    text-align: left;
    font-family: "JetBrains Mono", monospace;
}

.footer-container {
    font-size: 1rem;
    flex: 1;
    padding: 1rem 0;
    justify-content: center;
    align-items: center;
    width: 100vw;
    background-color: aba7a7;

    .sub-heading::after {
        height: 0px;
    }

    .section {
        margin-top: 5rem;
        margin-bottom: 0;
    }

    .footer {
        display: flex;
        justify-content: center;
        align-items: center;

        p {
            color: grey;
        }

        a {
            color: darkgrey;
            text-decoration: none;
            background-image: linear-gradient(grey, grey);
            background-size: 0% 0.1em;
            background-position-y: 100%;
            background-position-x: 0%;
            background-repeat: no-repeat;
            transition: background-size 0.3s ease-in-out;
        }

        a:hover,
        a:focus,
        a:active {
            background-size: 100% 0.1em;
        }
    }

    .footer-socials {
        height: 1rem;

        .link {
            color: grey;
            padding: 0;

            .favicon {
                transition: transform 0.5s ease;
                height: 2.8rem;
                width: 2.8rem;
            }

            .favicon:hover {
                text-decoration: none;
                transform: scale(1.3);
                color: lime;
            }
        }
    }
}

hr {
    border-top: 4px solid rgb(27, 226, 27);
}

.hidden {
    display: none;
}

// Contact Section Styles //

.contact-section {
    div {
        border-radius: 1rem;
    }

    fieldset {
        border-color: grey !important;
        border: 1px;
    }

    .Mui-focused {
        color: lime !important;
    }

    .text-field {
        background-color: #474747;
        border-radius: 1rem;
    }

    .textarea {
        border-radius: 1rem !important;
    }

    .contact-button {
        color: "lime";
        border-color: "lime";
        font-family: "JetBrains Mono", monospace !important;
    }

    .contact-button:hover {
        background-color: #474747 !important;
    }

    label {
        color: grey !important;
        font-family: "JetBrains Mono", monospace !important;
    }
}
